{"name": "k-mean", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"chart.js": "^4.3.2", "d3": "^7.9.0", "echarts": "^6.0.0", "element-plus": "^2.11.3", "vue": "^3.5.21", "vue-router": "^4.5.1"}, "devDependencies": {"@vitejs/plugin-vue": "^6.0.1", "@vue/tsconfig": "^0.8.1", "autoprefixer": "^10.4.21", "less": "^4.4.1", "postcss": "^8.5.6", "tailwindcss": "^4.1.14", "typescript": "~5.8.3", "vite": "npm:rolldown-vite@7.1.12", "vue-tsc": "^3.0.7"}, "overrides": {"vite": "npm:rolldown-vite@7.1.12"}}