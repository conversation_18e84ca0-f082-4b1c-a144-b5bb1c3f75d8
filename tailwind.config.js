/** @type {import('tailwindcss').Config} */
module.exports = {
    // 配置需要扫描的文件，确保 Tailwind 能识别所有使用类名的地方
    content: [
      "./index.html",
      "./src/**/*.{vue,js,ts,jsx,tsx}",
    ],
    theme: {
      extend: {
        // 自定义主题（颜色、字体等）
        colors: {
          primary: '#4F46E5', // 靛蓝色作为主色调
          secondary: '#10B981', // 绿色作为辅助色
        },
        fontFamily: {
          inter: ['Inter', 'sans-serif'],
        },
      },
    },
    plugins: [],
  }
      