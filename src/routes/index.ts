/*
 * @Description: 
 * @Version: 2.0
 * @Autor: hjw
 * @Date: 2025-09-24 21:45:24
 * @LastEditors: hjw
 * @LastEditTime: 2025-10-14 00:54:17
 */
import { createRouter, createWebHistory } from "vue-router";
 
 
let routes= [
    {
        path: '/ailab/k-mean',
        name: 'k-mean',
        //使用import可以路由懒加载，如果不使用，太多组件一起加载会造成白屏
        component: () => import('../views/k-mean/index.vue')
    },
    //{
        //配置404页面
        //path: '/:catchAll(.*)',
        //name: '404',
        //component: () => import(''),
    //}
]
// 路由
const router = createRouter({
    history: createWebHistory(),
    routes
})
// 导出
export default router 